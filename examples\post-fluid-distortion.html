<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • Post Fluid Distortion</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <div class="Info">
            Post Fluid Distortion. Based on shaders by
            <a href="https://github.com/PavelDoGreat/WebGL-Fluid-Simulation" target="_blank"><PERSON></a>
        </div>
        <script type="module">
            // 导入OGL库的核心组件
            // Renderer: WebGL渲染器
            // Camera: 相机控制
            // RenderTarget: 渲染目标（帧缓冲对象）
            // Geometry: 几何体定义
            // Program: 着色器程序
            // Mesh: 网格对象
            // Color: 颜色工具类
            // Vec2: 二维向量
            // Box: 立方体几何体
            // NormalProgram: 法线着色器程序
            // Post: 后处理效果管理器
            import { Renderer, Camera, RenderTarget, Geometry, Program, Mesh, Color, Vec2, Box, NormalProgram, Post } from '../src/index.js';

            // 后处理片段着色器 - 用于将流体模拟结果应用到最终渲染的场景上
            const fragment = /* glsl */ `
                precision highp float;

                // 输入纹理
                uniform sampler2D tMap;    // 原始场景渲染结果
                uniform sampler2D tFluid;  // 流体模拟结果纹理
                uniform float uTime;       // 时间uniform，用于动画效果
                varying vec2 vUv;          // 纹理坐标

                void main() {
                    // 从流体纹理中采样流体数据（速度和密度信息）
                    vec3 fluid = texture2D(tFluid, vUv).rgb;

                    // 使用流体的红绿通道作为扭曲偏移量
                    // 0.0002是扭曲强度系数，控制扭曲程度
                    vec2 uv = vUv - fluid.rg * 0.0002;

                    // 混合扭曲后的场景和流体可视化
                    // step(0.5, vUv.x)在屏幕中央创建一个分割线
                    // 左半边显示扭曲效果，右半边显示流体可视化
                    gl_FragColor = mix( texture2D(tMap, uv), vec4(fluid * 0.1 + 0.5, 1), step(0.5, vUv.x) ) ;

                    // 备选效果：基于时间的振荡混合（已注释）
                    // gl_FragColor = mix(texture2D(tMap, uv), vec4(fluid * 0.1 + 0.5, 1), smoothstep(0.0, 0.7, sin(uTime)));
                }
            `;

            // 基础顶点着色器 - 用于流体模拟的各种计算步骤
            const baseVertex = /* glsl */ `
                precision highp float;
                attribute vec2 position;   // 顶点位置属性
                attribute vec2 uv;         // 纹理坐标属性
                varying vec2 vUv;          // 当前像素的纹理坐标
                varying vec2 vL;           // 左邻居像素的纹理坐标
                varying vec2 vR;           // 右邻居像素的纹理坐标
                varying vec2 vT;           // 上邻居像素的纹理坐标
                varying vec2 vB;           // 下邻居像素的纹理坐标
                uniform vec2 texelSize;    // 单个纹理像素的大小
                void main () {
                    vUv = uv;
                    // 计算相邻像素的纹理坐标，用于有限差分计算
                    vL = vUv - vec2(texelSize.x, 0.0);  // 左邻居
                    vR = vUv + vec2(texelSize.x, 0.0);  // 右邻居
                    vT = vUv + vec2(0.0, texelSize.y);  // 上邻居
                    vB = vUv - vec2(0.0, texelSize.y);  // 下邻居
                    gl_Position = vec4(position, 0, 1);
                }
            `;

            // 清除着色器 - 用于清除或衰减纹理内容
            const clearShader = /* glsl */ `
                precision mediump float;
                precision mediump sampler2D;
                varying highp vec2 vUv;
                uniform sampler2D uTexture;  // 要清除的纹理
                uniform float value;         // 衰减系数（0-1之间）
                void main () {
                    // 将纹理内容乘以衰减系数，实现逐渐消散效果
                    gl_FragColor = value * texture2D(uTexture, vUv);
                }
            `;

            // 飞溅着色器 - 用于在流体中添加输入（鼠标/触摸交互）
            const splatShader = /* glsl */ `
                precision highp float;
                precision highp sampler2D;
                varying vec2 vUv;
                uniform sampler2D uTarget;   // 目标纹理（速度场或密度场）
                uniform float aspectRatio;   // 屏幕宽高比
                uniform vec3 color;          // 飞溅的颜色/强度
                uniform vec2 point;          // 飞溅的中心点
                uniform float radius;        // 飞溅的半径
                void main () {
                    // 计算当前像素到飞溅中心的距离
                    vec2 p = vUv - point.xy;
                    p.x *= aspectRatio;  // 校正宽高比

                    // 使用高斯函数创建圆形飞溅效果
                    vec3 splat = exp(-dot(p, p) / radius) * color;

                    // 将飞溅效果叠加到现有内容上
                    vec3 base = texture2D(uTarget, vUv).xyz;
                    gl_FragColor = vec4(base + splat, 1.0);
                }
            `;

            // 手动双线性过滤的平流着色器 - 用于不支持线性过滤的设备
            const advectionManualFilteringShader = /* glsl */ `
                precision highp float;
                precision highp sampler2D;
                varying vec2 vUv;
                uniform sampler2D uVelocity;    // 速度场纹理
                uniform sampler2D uSource;      // 要平流的源纹理（速度或密度）
                uniform vec2 texelSize;         // 速度场的纹理像素大小
                uniform vec2 dyeTexelSize;      // 密度场的纹理像素大小
                uniform float dt;               // 时间步长
                uniform float dissipation;      // 消散系数

                // 手动实现双线性插值函数
                vec4 bilerp (sampler2D sam, vec2 uv, vec2 tsize) {
                    vec2 st = uv / tsize - 0.5;
                    vec2 iuv = floor(st);  // 整数部分
                    vec2 fuv = fract(st);  // 小数部分

                    // 采样四个相邻像素
                    vec4 a = texture2D(sam, (iuv + vec2(0.5, 0.5)) * tsize);
                    vec4 b = texture2D(sam, (iuv + vec2(1.5, 0.5)) * tsize);
                    vec4 c = texture2D(sam, (iuv + vec2(0.5, 1.5)) * tsize);
                    vec4 d = texture2D(sam, (iuv + vec2(1.5, 1.5)) * tsize);

                    // 执行双线性插值
                    return mix(mix(a, b, fuv.x), mix(c, d, fuv.x), fuv.y);
                }

                void main () {
                    // 根据速度场计算反向追踪的坐标
                    vec2 coord = vUv - dt * bilerp(uVelocity, vUv, texelSize).xy * texelSize;

                    // 从反向追踪的位置采样源纹理，并应用消散
                    gl_FragColor = dissipation * bilerp(uSource, coord, dyeTexelSize);
                    gl_FragColor.a = 1.0;
                }
            `;

            // 简化版平流着色器 - 使用硬件线性过滤
            const advectionShader = /* glsl */ `
                precision highp float;
                precision highp sampler2D;
                varying vec2 vUv;
                uniform sampler2D uVelocity;    // 速度场纹理
                uniform sampler2D uSource;      // 要平流的源纹理
                uniform vec2 texelSize;         // 纹理像素大小
                uniform float dt;               // 时间步长
                uniform float dissipation;      // 消散系数
                void main () {
                    // 使用硬件线性过滤进行反向追踪采样
                    vec2 coord = vUv - dt * texture2D(uVelocity, vUv).xy * texelSize;
                    gl_FragColor = dissipation * texture2D(uSource, coord);
                    gl_FragColor.a = 1.0;
                }
            `;

            // 散度计算着色器 - 计算速度场的散度（用于压力求解）
            const divergenceShader = /* glsl */ `
                precision mediump float;
                precision mediump sampler2D;
                varying highp vec2 vUv;
                varying highp vec2 vL;   // 左邻居坐标
                varying highp vec2 vR;   // 右邻居坐标
                varying highp vec2 vT;   // 上邻居坐标
                varying highp vec2 vB;   // 下邻居坐标
                uniform sampler2D uVelocity;
                void main () {
                    // 采样相邻像素的速度分量
                    float L = texture2D(uVelocity, vL).x;  // 左邻居的x速度
                    float R = texture2D(uVelocity, vR).x;  // 右邻居的x速度
                    float T = texture2D(uVelocity, vT).y;  // 上邻居的y速度
                    float B = texture2D(uVelocity, vB).y;  // 下邻居的y速度
                    vec2 C = texture2D(uVelocity, vUv).xy; // 当前像素的速度

                    // 边界条件处理（固体边界）
                    if (vL.x < 0.0) { L = -C.x; }  // 左边界
                    if (vR.x > 1.0) { R = -C.x; }  // 右边界
                    if (vT.y > 1.0) { T = -C.y; }  // 上边界
                    if (vB.y < 0.0) { B = -C.y; }  // 下边界

                    // 计算散度：∇·v = ∂u/∂x + ∂v/∂y
                    float div = 0.5 * (R - L + T - B);
                    gl_FragColor = vec4(div, 0.0, 0.0, 1.0);
                }
            `;

            // 旋度计算着色器 - 计算速度场的旋度（涡量）
            const curlShader = /* glsl */ `
                precision mediump float;
                precision mediump sampler2D;
                varying highp vec2 vUv;
                varying highp vec2 vL;   // 左邻居坐标
                varying highp vec2 vR;   // 右邻居坐标
                varying highp vec2 vT;   // 上邻居坐标
                varying highp vec2 vB;   // 下邻居坐标
                uniform sampler2D uVelocity;
                void main () {
                    // 采样相邻像素的速度分量（注意这里采样的是交叉分量）
                    float L = texture2D(uVelocity, vL).y;  // 左邻居的y速度
                    float R = texture2D(uVelocity, vR).y;  // 右邻居的y速度
                    float T = texture2D(uVelocity, vT).x;  // 上邻居的x速度
                    float B = texture2D(uVelocity, vB).x;  // 下邻居的x速度

                    // 计算旋度：curl = ∂v/∂x - ∂u/∂y
                    float vorticity = R - L - T + B;
                    gl_FragColor = vec4(0.5 * vorticity, 0.0, 0.0, 1.0);
                }
            `;

            // 涡量约束着色器 - 通过添加涡量力来增强流体的旋转特性
            const vorticityShader = /* glsl */ `
                precision highp float;
                precision highp sampler2D;
                varying vec2 vUv;
                varying vec2 vL;   // 左邻居坐标
                varying vec2 vR;   // 右邻居坐标
                varying vec2 vT;   // 上邻居坐标
                varying vec2 vB;   // 下邻居坐标
                uniform sampler2D uVelocity;  // 当前速度场
                uniform sampler2D uCurl;      // 旋度场
                uniform float curl;           // 涡量强度系数
                uniform float dt;             // 时间步长
                void main () {
                    // 采样相邻像素的旋度值
                    float L = texture2D(uCurl, vL).x;
                    float R = texture2D(uCurl, vR).x;
                    float T = texture2D(uCurl, vT).x;
                    float B = texture2D(uCurl, vB).x;
                    float C = texture2D(uCurl, vUv).x;  // 当前像素的旋度

                    // 计算涡量梯度的方向
                    vec2 force = 0.5 * vec2(abs(T) - abs(B), abs(R) - abs(L));
                    force /= length(force) + 0.0001;  // 归一化，避免除零
                    force *= curl * C;  // 应用涡量强度
                    force.y *= -1.0;    // 翻转y方向

                    // 将涡量力添加到现有速度上
                    vec2 vel = texture2D(uVelocity, vUv).xy;
                    gl_FragColor = vec4(vel + force * dt, 0.0, 1.0);
                }
            `;

            // 压力求解着色器 - 使用雅可比迭代法求解泊松方程
            const pressureShader = /* glsl */ `
                precision mediump float;
                precision mediump sampler2D;
                varying highp vec2 vUv;
                varying highp vec2 vL;   // 左邻居坐标
                varying highp vec2 vR;   // 右邻居坐标
                varying highp vec2 vT;   // 上邻居坐标
                varying highp vec2 vB;   // 下邻居坐标
                uniform sampler2D uPressure;    // 当前压力场
                uniform sampler2D uDivergence;  // 散度场
                void main () {
                    // 采样相邻像素的压力值
                    float L = texture2D(uPressure, vL).x;
                    float R = texture2D(uPressure, vR).x;
                    float T = texture2D(uPressure, vT).x;
                    float B = texture2D(uPressure, vB).x;
                    float C = texture2D(uPressure, vUv).x;  // 当前压力（未使用）
                    float divergence = texture2D(uDivergence, vUv).x;  // 当前散度

                    // 雅可比迭代：p_new = (p_left + p_right + p_top + p_bottom - divergence) / 4
                    float pressure = (L + R + B + T - divergence) * 0.25;
                    gl_FragColor = vec4(pressure, 0.0, 0.0, 1.0);
                }
            `;

            // 梯度减法着色器 - 从速度场中减去压力梯度，使速度场无散度
            const gradientSubtractShader = /* glsl */ `
                precision mediump float;
                precision mediump sampler2D;
                varying highp vec2 vUv;
                varying highp vec2 vL;   // 左邻居坐标
                varying highp vec2 vR;   // 右邻居坐标
                varying highp vec2 vT;   // 上邻居坐标
                varying highp vec2 vB;   // 下邻居坐标
                uniform sampler2D uPressure;  // 压力场
                uniform sampler2D uVelocity;  // 当前速度场
                void main () {
                    // 采样相邻像素的压力值
                    float L = texture2D(uPressure, vL).x;
                    float R = texture2D(uPressure, vR).x;
                    float T = texture2D(uPressure, vT).x;
                    float B = texture2D(uPressure, vB).x;

                    // 获取当前速度
                    vec2 velocity = texture2D(uVelocity, vUv).xy;

                    // 计算压力梯度并从速度中减去：v_new = v - ∇p
                    velocity.xy -= vec2(R - L, T - B);
                    gl_FragColor = vec4(velocity, 0.0, 1.0);
                }
            `;

            {
                // === 初始化渲染器和基础设置 ===
                const renderer = new Renderer({ dpr: 2 }); // 创建渲染器，设备像素比为2
                const gl = renderer.gl; // 获取WebGL上下文
                document.body.appendChild(gl.canvas); // 将画布添加到页面
                gl.clearColor(1, 1, 1, 1); // 设置清除颜色为白色

                // 创建相机
                const camera = new Camera(gl, { fov: 35 });
                camera.position.set(0, 1, 5); // 设置相机位置
                camera.lookAt([0, 0, 0]); // 相机看向原点

                // 创建后处理管理器
                const post = new Post(gl);

                // 窗口大小调整处理函数
                function resize() {
                    renderer.setSize(window.innerWidth, window.innerHeight); // 调整渲染器大小
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height }); // 更新相机宽高比
                    post.resize(); // 调整后处理大小
                }
                window.addEventListener('resize', resize, false);
                resize(); // 初始调用

                // === 设备兼容性辅助函数 ===

                // 获取设备支持的纹理格式（降级处理）
                function getSupportedFormat(gl, internalFormat, format, type) {
                    if (!supportRenderTextureFormat(gl, internalFormat, format, type)) {
                        switch (internalFormat) {
                            case gl.R16F:
                                // 如果不支持单通道16位浮点，尝试双通道
                                return getSupportedFormat(gl, gl.RG16F, gl.RG, type);
                            case gl.RG16F:
                                // 如果不支持双通道16位浮点，尝试四通道
                                return getSupportedFormat(gl, gl.RGBA16F, gl.RGBA, type);
                            default:
                                return null;
                        }
                    }

                    return { internalFormat, format };
                }

                // 检测设备是否支持特定的渲染纹理格式
                function supportRenderTextureFormat(gl, internalFormat, format, type) {
                    // 创建测试纹理
                    let texture = gl.createTexture();
                    gl.bindTexture(gl.TEXTURE_2D, texture);
                    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.NEAREST);
                    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.NEAREST);
                    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
                    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
                    gl.texImage2D(gl.TEXTURE_2D, 0, internalFormat, 4, 4, 0, format, type, null);

                    // 创建帧缓冲对象并尝试附加纹理
                    let fbo = gl.createFramebuffer();
                    gl.bindFramebuffer(gl.FRAMEBUFFER, fbo);
                    gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture, 0);

                    // 检查帧缓冲完整性
                    const status = gl.checkFramebufferStatus(gl.FRAMEBUFFER);
                    if (status != gl.FRAMEBUFFER_COMPLETE) return false;
                    return true;
                }

                // 创建双缓冲帧缓冲对象对 - 用于GPU上的乒乓渲染
                function createDoubleFBO(gl, { width, height, wrapS, wrapT, minFilter = gl.LINEAR, magFilter = minFilter, type, format, internalFormat, depth } = {}) {
                    const options = { width, height, wrapS, wrapT, minFilter, magFilter, type, format, internalFormat, depth };
                    const fbo = {
                        read: new RenderTarget(gl, options), // 读取缓冲区
                        write: new RenderTarget(gl, options), // 写入缓冲区
                        swap: () => {
                            // 交换读写缓冲区的函数
                            let temp = fbo.read;
                            fbo.read = fbo.write;
                            fbo.write = temp;
                        },
                    };
                    return fbo;
                }

                // === 流体模拟参数配置 ===

                // 模拟分辨率设置
                const simRes = 128; // 速度场和压力场的分辨率（较低以提高性能）
                const dyeRes = 512; // 密度场的分辨率（较高以获得更好的视觉效果）

                // 流体行为控制参数
                const iterations = 3; // 压力求解的迭代次数
                const densityDissipation = 0.97; // 密度消散率（0-1，越小消散越快）
                const velocityDissipation = 0.98; // 速度消散率（0-1，越小消散越快）
                const pressureDissipation = 0.8; // 压力消散率
                const curlStrength = 20; // 涡量强度（控制旋转效果）
                const radius = 0.2; // 输入飞溅的半径

                // 通用uniform变量
                const texelSize = { value: new Vec2(1 / simRes) }; // 纹理像素大小

                // === 获取设备支持的纹理格式和类型 ===

                // 检查是否支持半精度浮点线性过滤
                let supportLinearFiltering = gl.renderer.extensions[`OES_texture_${gl.renderer.isWebgl2 ? `` : `half_`}float_linear`];

                // 获取半精度浮点类型常量
                const halfFloat = gl.renderer.isWebgl2 ? gl.HALF_FLOAT : gl.renderer.extensions['OES_texture_half_float'].HALF_FLOAT_OES;

                // 根据设备支持情况选择过滤方式
                const filtering = supportLinearFiltering ? gl.LINEAR : gl.NEAREST;
                let rgba, rg, r; // 不同通道数的纹理格式

                if (gl.renderer.isWebgl2) {
                    // WebGL2支持更多精确的纹理格式
                    rgba = getSupportedFormat(gl, gl.RGBA16F, gl.RGBA, halfFloat); // 四通道16位浮点
                    rg = getSupportedFormat(gl, gl.RG16F, gl.RG, halfFloat); // 双通道16位浮点
                    r = getSupportedFormat(gl, gl.R16F, gl.RED, halfFloat); // 单通道16位浮点
                } else {
                    // WebGL1的降级处理
                    rgba = getSupportedFormat(gl, gl.RGBA, gl.RGBA, halfFloat);
                    rg = rgba; // 降级到四通道
                    r = rgba; // 降级到四通道
                }

                // === 创建流体模拟所需的帧缓冲对象 ===

                // 密度场（染料）- 用于存储流体的可视化信息
                const density = createDoubleFBO(gl, {
                    width: dyeRes, // 使用高分辨率获得更好的视觉效果
                    height: dyeRes,
                    type: halfFloat, // 半精度浮点类型
                    format: rgba?.format, // 四通道格式（RGBA）
                    internalFormat: rgba?.internalFormat,
                    minFilter: filtering, // 根据设备支持选择过滤方式
                    depth: false, // 不需要深度缓冲
                });

                // 速度场 - 存储流体的速度信息
                const velocity = createDoubleFBO(gl, {
                    width: simRes, // 使用较低分辨率提高性能
                    height: simRes,
                    type: halfFloat,
                    format: rg?.format, // 双通道格式（RG，存储x和y速度）
                    internalFormat: rg?.internalFormat,
                    minFilter: filtering,
                    depth: false,
                });

                // 压力场 - 用于压力投影步骤
                const pressure = createDoubleFBO(gl, {
                    width: simRes,
                    height: simRes,
                    type: halfFloat,
                    format: r?.format, // 单通道格式（只需要标量压力值）
                    internalFormat: r?.internalFormat,
                    minFilter: gl.NEAREST, // 压力场使用最近邻过滤
                    depth: false,
                });

                // 散度场 - 存储速度场的散度（单次渲染目标）
                const divergence = new RenderTarget(gl, {
                    width: simRes,
                    height: simRes,
                    type: halfFloat,
                    format: r?.format, // 单通道格式
                    internalFormat: r?.internalFormat,
                    minFilter: gl.NEAREST,
                    depth: false,
                });

                // 旋度场 - 存储速度场的旋度（涡量）
                const curl = new RenderTarget(gl, {
                    width: simRes,
                    height: simRes,
                    type: halfFloat,
                    format: r?.format, // 单通道格式
                    internalFormat: r?.internalFormat,
                    minFilter: gl.NEAREST,
                    depth: false,
                });

                // === 创建用于模拟计算的几何体 ===
                // 全屏三角形 - 用于所有流体模拟的着色器程序
                const triangle = new Geometry(gl, {
                    // 位置数据：创建一个覆盖整个屏幕的大三角形
                    position: { size: 2, data: new Float32Array([-1, -1, 3, -1, -1, 3]) },
                    // UV坐标：对应的纹理坐标
                    uv: { size: 2, data: new Float32Array([0, 0, 2, 0, 0, 2]) },
                });

                // === 创建流体模拟着色器程序 ===

                // 清除程序 - 用于清除或衰减纹理内容
                const clearProgram = new Mesh(gl, {
                    geometry: triangle,
                    program: new Program(gl, {
                        vertex: baseVertex,
                        fragment: clearShader,
                        uniforms: {
                            texelSize, // 纹理像素大小
                            uTexture: { value: null }, // 要清除的纹理（运行时设置）
                            value: { value: pressureDissipation }, // 衰减系数
                        },
                        depthTest: false, // 禁用深度测试
                        depthWrite: false, // 禁用深度写入
                    }),
                });

                // 飞溅程序 - 用于添加用户输入（鼠标/触摸交互）
                const splatProgram = new Mesh(gl, {
                    geometry: triangle,
                    program: new Program(gl, {
                        vertex: baseVertex,
                        fragment: splatShader,
                        uniforms: {
                            texelSize, // 纹理像素大小
                            uTarget: { value: null }, // 目标纹理（运行时设置）
                            aspectRatio: { value: 1 }, // 屏幕宽高比
                            color: { value: new Color() }, // 飞溅颜色/强度
                            point: { value: new Vec2() }, // 飞溅中心点
                            radius: { value: radius / 100 }, // 飞溅半径
                        },
                        depthTest: false,
                        depthWrite: false,
                    }),
                });

                // 平流程序 - 用于移动速度场和密度场
                const advectionProgram = new Mesh(gl, {
                    geometry: triangle,
                    program: new Program(gl, {
                        vertex: baseVertex,
                        // 根据设备支持情况选择着色器版本
                        fragment: supportLinearFiltering ? advectionShader : advectionManualFilteringShader,
                        uniforms: {
                            texelSize, // 速度场纹理像素大小
                            dyeTexelSize: { value: new Vec2(1 / dyeRes) }, // 密度场纹理像素大小
                            uVelocity: { value: null }, // 速度场纹理
                            uSource: { value: null }, // 源纹理（要平流的内容）
                            dt: { value: 0.016 }, // 时间步长（约60fps）
                            dissipation: { value: 1 }, // 消散系数（运行时设置）
                        },
                        depthTest: false,
                        depthWrite: false,
                    }),
                });

                // 散度计算程序 - 计算速度场的散度
                const divergenceProgram = new Mesh(gl, {
                    geometry: triangle,
                    program: new Program(gl, {
                        vertex: baseVertex,
                        fragment: divergenceShader,
                        uniforms: {
                            texelSize, // 纹理像素大小
                            uVelocity: { value: null }, // 速度场纹理
                        },
                        depthTest: false,
                        depthWrite: false,
                    }),
                });

                // 旋度计算程序 - 计算速度场的旋度（涡量）
                const curlProgram = new Mesh(gl, {
                    geometry: triangle,
                    program: new Program(gl, {
                        vertex: baseVertex,
                        fragment: curlShader,
                        uniforms: {
                            texelSize, // 纹理像素大小
                            uVelocity: { value: null }, // 速度场纹理
                        },
                        depthTest: false,
                        depthWrite: false,
                    }),
                });

                // 涡量约束程序 - 通过添加涡量力增强流体旋转
                const vorticityProgram = new Mesh(gl, {
                    geometry: triangle,
                    program: new Program(gl, {
                        vertex: baseVertex,
                        fragment: vorticityShader,
                        uniforms: {
                            texelSize, // 纹理像素大小
                            uVelocity: { value: null }, // 速度场纹理
                            uCurl: { value: null }, // 旋度场纹理
                            curl: { value: curlStrength }, // 涡量强度系数
                            dt: { value: 0.016 }, // 时间步长
                        },
                        depthTest: false,
                        depthWrite: false,
                    }),
                });

                // 压力求解程序 - 使用雅可比迭代法求解泊松方程
                const pressureProgram = new Mesh(gl, {
                    geometry: triangle,
                    program: new Program(gl, {
                        vertex: baseVertex,
                        fragment: pressureShader,
                        uniforms: {
                            texelSize, // 纹理像素大小
                            uPressure: { value: null }, // 当前压力场纹理
                            uDivergence: { value: null }, // 散度场纹理
                        },
                        depthTest: false,
                        depthWrite: false,
                    }),
                });

                // 梯度减法程序 - 从速度场中减去压力梯度，实现无散度约束
                const gradientSubtractProgram = new Mesh(gl, {
                    geometry: triangle,
                    program: new Program(gl, {
                        vertex: baseVertex,
                        fragment: gradientSubtractShader,
                        uniforms: {
                            texelSize, // 纹理像素大小
                            uPressure: { value: null }, // 压力场纹理
                            uVelocity: { value: null }, // 速度场纹理
                        },
                        depthTest: false,
                        depthWrite: false,
                    }),
                });

                // === 用户交互处理 ===

                // 存储待处理的飞溅事件
                const splats = [];

                // Create handlers to get mouse position and velocity
                const isTouchCapable = 'ontouchstart' in window;
                if (isTouchCapable) {
                    window.addEventListener('touchstart', updateMouse, false);
                    window.addEventListener('touchmove', updateMouse, false);
                } else {
                    window.addEventListener('mousemove', updateMouse, false);
                }

                const lastMouse = new Vec2();
                function updateMouse(e) {
                    if (e.changedTouches && e.changedTouches.length) {
                        e.x = e.changedTouches[0].pageX;
                        e.y = e.changedTouches[0].pageY;
                    }
                    if (e.x === undefined) {
                        e.x = e.pageX;
                        e.y = e.pageY;
                    }

                    if (!lastMouse.isInit) {
                        lastMouse.isInit = true;

                        // First input
                        lastMouse.set(e.x, e.y);
                    }

                    const deltaX = e.x - lastMouse.x;
                    const deltaY = e.y - lastMouse.y;

                    lastMouse.set(e.x, e.y);

                    // Add if the mouse is moving
                    if (Math.abs(deltaX) || Math.abs(deltaY)) {
                        splats.push({
                            // Get mouse value in 0 to 1 range, with y flipped
                            x: e.x / gl.renderer.width,
                            y: 1 - e.y / gl.renderer.height,
                            dx: deltaX * 5,
                            dy: deltaY * -5,
                        });
                    }
                }

                // Function to draw number of interactions onto input render target
                function splat({ x, y, dx, dy }) {
                    splatProgram.program.uniforms.uTarget.value = velocity.read.texture;
                    splatProgram.program.uniforms.aspectRatio.value = gl.renderer.width / gl.renderer.height;
                    splatProgram.program.uniforms.point.value.set(x, y);
                    splatProgram.program.uniforms.color.value.set(dx, dy, 1);

                    gl.renderer.render({
                        scene: splatProgram,
                        target: velocity.write,
                        sort: false,
                        update: false,
                    });
                    velocity.swap();

                    splatProgram.program.uniforms.uTarget.value = density.read.texture;

                    gl.renderer.render({
                        scene: splatProgram,
                        target: density.write,
                        sort: false,
                        update: false,
                    });
                    density.swap();
                }

                // Create initial scene
                const geometry = new Box(gl);
                const mesh = new Mesh(gl, { geometry, program: new NormalProgram(gl) });

                for (let i = 0; i < 20; i++) {
                    const m = new Mesh(gl, { geometry, program: new NormalProgram(gl) });
                    m.position.set(Math.random() * 3 - 1.5, Math.random() * 3 - 1.5, Math.random() * 3 - 1.5);
                    m.rotation.set(Math.random() * 6.28 - 3.14, Math.random() * 6.28 - 3.14, 0);
                    m.scale.set(Math.random() * 0.5 + 0.1);
                    m.setParent(mesh);
                }

                const pass = post.addPass({
                    fragment,
                    uniforms: {
                        tFluid: { value: null },
                        uTime: { value: 0 },
                    },
                });

                requestAnimationFrame(update);
                function update(t) {
                    requestAnimationFrame(update);

                    // Perform all of the fluid simulation renders
                    // No need to clear during sim, saving a number of GL calls.
                    gl.renderer.autoClear = false;

                    // Render all of the inputs since last frame
                    for (let i = splats.length - 1; i >= 0; i--) {
                        splat(splats.splice(i, 1)[0]);
                    }

                    curlProgram.program.uniforms.uVelocity.value = velocity.read.texture;

                    gl.renderer.render({
                        scene: curlProgram,
                        target: curl,
                        sort: false,
                        update: false,
                    });

                    vorticityProgram.program.uniforms.uVelocity.value = velocity.read.texture;
                    vorticityProgram.program.uniforms.uCurl.value = curl.texture;

                    gl.renderer.render({
                        scene: vorticityProgram,
                        target: velocity.write,
                        sort: false,
                        update: false,
                    });
                    velocity.swap();

                    divergenceProgram.program.uniforms.uVelocity.value = velocity.read.texture;

                    gl.renderer.render({
                        scene: divergenceProgram,
                        target: divergence,
                        sort: false,
                        update: false,
                    });

                    clearProgram.program.uniforms.uTexture.value = pressure.read.texture;

                    gl.renderer.render({
                        scene: clearProgram,
                        target: pressure.write,
                        sort: false,
                        update: false,
                    });
                    pressure.swap();

                    pressureProgram.program.uniforms.uDivergence.value = divergence.texture;

                    for (let i = 0; i < iterations; i++) {
                        pressureProgram.program.uniforms.uPressure.value = pressure.read.texture;

                        gl.renderer.render({
                            scene: pressureProgram,
                            target: pressure.write,
                            sort: false,
                            update: false,
                        });
                        pressure.swap();
                    }

                    gradientSubtractProgram.program.uniforms.uPressure.value = pressure.read.texture;
                    gradientSubtractProgram.program.uniforms.uVelocity.value = velocity.read.texture;

                    gl.renderer.render({
                        scene: gradientSubtractProgram,
                        target: velocity.write,
                        sort: false,
                        update: false,
                    });
                    velocity.swap();

                    advectionProgram.program.uniforms.dyeTexelSize.value.set(1 / simRes);
                    advectionProgram.program.uniforms.uVelocity.value = velocity.read.texture;
                    advectionProgram.program.uniforms.uSource.value = velocity.read.texture;
                    advectionProgram.program.uniforms.dissipation.value = velocityDissipation;

                    gl.renderer.render({
                        scene: advectionProgram,
                        target: velocity.write,
                        sort: false,
                        update: false,
                    });
                    velocity.swap();

                    advectionProgram.program.uniforms.dyeTexelSize.value.set(1 / dyeRes);
                    advectionProgram.program.uniforms.uVelocity.value = velocity.read.texture;
                    advectionProgram.program.uniforms.uSource.value = density.read.texture;
                    advectionProgram.program.uniforms.dissipation.value = densityDissipation;

                    gl.renderer.render({
                        scene: advectionProgram,
                        target: density.write,
                        sort: false,
                        update: false,
                    });
                    density.swap();

                    // Set clear back to default
                    gl.renderer.autoClear = true;

                    // Update post pass uniform with the simulation output
                    pass.uniforms.tFluid.value = density.read.texture;

                    mesh.rotation.y -= 0.0025;
                    mesh.rotation.x -= 0.005;

                    pass.uniforms.uTime.value = t * 0.001;

                    // Replace Renderer.render with post.render. Use the same arguments.
                    post.render({ scene: mesh, camera });
                }
            }
        </script>
    </body>
</html>
